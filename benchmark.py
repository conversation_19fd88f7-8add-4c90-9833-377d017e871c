#!/usr/bin/env python3
"""
GPU vs CPU 性能基準測試腳本
測試MapleStory自動練級機器人的GPU加速效果
"""

import cv2
import numpy as np
import time
import argparse
import os
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import json

# 導入項目模組
from util import find_pattern_sqdiff, load_image, get_gpu_info
from gpu_utils import benchmark_gpu_vs_cpu, gpu_template_matching, gpu_gaussian_blur, gpu_resize, gpu_cvt_color

def load_test_images() -> Tuple[np.ndarray, List[np.ndarray]]:
    """
    載入測試圖像
    
    Returns:
        測試圖像和模板列表
    """
    # 創建測試圖像（如果沒有真實圖像）
    test_image = np.random.randint(0, 255, (800, 1200, 3), dtype=np.uint8)
    
    # 創建一些測試模板
    templates = []
    for i in range(5):
        template = np.random.randint(0, 255, (50 + i*10, 50 + i*10, 3), dtype=np.uint8)
        templates.append(template)
    
    # 嘗試載入真實的遊戲圖像（如果存在）
    try:
        if os.path.exists("images/nametag"):
            nametag_files = [f for f in os.listdir("images/nametag") if f.endswith(('.png', '.jpg', '.jpeg'))]
            if nametag_files:
                real_template = load_image(f"images/nametag/{nametag_files[0]}")
                if real_template is not None:
                    templates.append(real_template)
                    print(f"載入真實模板: {nametag_files[0]}")
        
        if os.path.exists("images/monsters"):
            monster_files = [f for f in os.listdir("images/monsters") if f.endswith(('.png', '.jpg', '.jpeg'))]
            if monster_files:
                real_template = load_image(f"images/monsters/{monster_files[0]}")
                if real_template is not None:
                    templates.append(real_template)
                    print(f"載入真實怪物模板: {monster_files[0]}")
                    
    except Exception as e:
        print(f"載入真實圖像時出錯: {e}")
    
    return test_image, templates

def benchmark_template_matching(image: np.ndarray, templates: List[np.ndarray], iterations: int = 50) -> Dict:
    """
    測試模板匹配性能
    
    Args:
        image: 測試圖像
        templates: 模板列表
        iterations: 測試迭代次數
    
    Returns:
        性能測試結果
    """
    print(f"\n🔍 模板匹配性能測試 (迭代次數: {iterations})")
    print("=" * 50)
    
    results = {
        'cpu_times': [],
        'gpu_times': [],
        'template_sizes': []
    }
    
    for i, template in enumerate(templates):
        print(f"測試模板 {i+1}/{len(templates)} - 尺寸: {template.shape}")
        
        # CPU測試
        start_time = time.time()
        for _ in range(iterations):
            cv2.matchTemplate(image, template, cv2.TM_SQDIFF_NORMED)
        cpu_time = time.time() - start_time
        
        # GPU測試
        start_time = time.time()
        for _ in range(iterations):
            gpu_template_matching(image, template, cv2.TM_SQDIFF_NORMED)
        gpu_time = time.time() - start_time
        
        speedup = cpu_time / gpu_time if gpu_time > 0 else 0
        
        print(f"  CPU時間: {cpu_time:.4f}秒")
        print(f"  GPU時間: {gpu_time:.4f}秒")
        print(f"  加速比: {speedup:.2f}x")
        print()
        
        results['cpu_times'].append(cpu_time)
        results['gpu_times'].append(gpu_time)
        results['template_sizes'].append(template.shape[:2])
    
    return results

def benchmark_image_processing(image: np.ndarray, iterations: int = 100) -> Dict:
    """
    測試圖像處理性能
    
    Args:
        image: 測試圖像
        iterations: 測試迭代次數
    
    Returns:
        性能測試結果
    """
    print(f"\n🖼️ 圖像處理性能測試 (迭代次數: {iterations})")
    print("=" * 50)
    
    results = {}
    
    # 測試高斯模糊
    print("測試高斯模糊...")
    start_time = time.time()
    for _ in range(iterations):
        cv2.GaussianBlur(image, (15, 15), 0)
    cpu_blur_time = time.time() - start_time
    
    start_time = time.time()
    for _ in range(iterations):
        gpu_gaussian_blur(image, (15, 15), 0)
    gpu_blur_time = time.time() - start_time
    
    blur_speedup = cpu_blur_time / gpu_blur_time if gpu_blur_time > 0 else 0
    print(f"  高斯模糊 - CPU: {cpu_blur_time:.4f}s, GPU: {gpu_blur_time:.4f}s, 加速比: {blur_speedup:.2f}x")
    
    # 測試圖像縮放
    print("測試圖像縮放...")
    target_size = (640, 480)
    start_time = time.time()
    for _ in range(iterations):
        cv2.resize(image, target_size)
    cpu_resize_time = time.time() - start_time
    
    start_time = time.time()
    for _ in range(iterations):
        gpu_resize(image, target_size)
    gpu_resize_time = time.time() - start_time
    
    resize_speedup = cpu_resize_time / gpu_resize_time if gpu_resize_time > 0 else 0
    print(f"  圖像縮放 - CPU: {cpu_resize_time:.4f}s, GPU: {gpu_resize_time:.4f}s, 加速比: {resize_speedup:.2f}x")
    
    # 測試顏色轉換
    print("測試顏色轉換...")
    start_time = time.time()
    for _ in range(iterations):
        cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    cpu_cvt_time = time.time() - start_time
    
    start_time = time.time()
    for _ in range(iterations):
        gpu_cvt_color(image, cv2.COLOR_BGR2GRAY)
    gpu_cvt_time = time.time() - start_time
    
    cvt_speedup = cpu_cvt_time / gpu_cvt_time if gpu_cvt_time > 0 else 0
    print(f"  顏色轉換 - CPU: {cpu_cvt_time:.4f}s, GPU: {gpu_cvt_time:.4f}s, 加速比: {cvt_speedup:.2f}x")
    
    results = {
        'blur': {'cpu': cpu_blur_time, 'gpu': gpu_blur_time, 'speedup': blur_speedup},
        'resize': {'cpu': cpu_resize_time, 'gpu': gpu_resize_time, 'speedup': resize_speedup},
        'cvt_color': {'cpu': cpu_cvt_time, 'gpu': gpu_cvt_time, 'speedup': cvt_speedup}
    }
    
    return results

def save_results(template_results: Dict, processing_results: Dict, output_file: str = "benchmark_results.json"):
    """
    保存測試結果到文件
    
    Args:
        template_results: 模板匹配結果
        processing_results: 圖像處理結果
        output_file: 輸出文件名
    """
    results = {
        'gpu_info': get_gpu_info(),
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'template_matching': template_results,
        'image_processing': processing_results
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 測試結果已保存到: {output_file}")

def plot_results(template_results: Dict, processing_results: Dict):
    """
    繪製性能比較圖表
    
    Args:
        template_results: 模板匹配結果
        processing_results: 圖像處理結果
    """
    try:
        # 創建圖表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 模板匹配性能圖
        if template_results['cpu_times'] and template_results['gpu_times']:
            template_indices = range(len(template_results['cpu_times']))
            ax1.bar([i - 0.2 for i in template_indices], template_results['cpu_times'], 
                   width=0.4, label='CPU', alpha=0.7)
            ax1.bar([i + 0.2 for i in template_indices], template_results['gpu_times'], 
                   width=0.4, label='GPU', alpha=0.7)
            ax1.set_xlabel('模板索引')
            ax1.set_ylabel('時間 (秒)')
            ax1.set_title('模板匹配性能比較')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        
        # 圖像處理性能圖
        operations = list(processing_results.keys())
        cpu_times = [processing_results[op]['cpu'] for op in operations]
        gpu_times = [processing_results[op]['gpu'] for op in operations]
        
        x = np.arange(len(operations))
        ax2.bar(x - 0.2, cpu_times, width=0.4, label='CPU', alpha=0.7)
        ax2.bar(x + 0.2, gpu_times, width=0.4, label='GPU', alpha=0.7)
        ax2.set_xlabel('操作類型')
        ax2.set_ylabel('時間 (秒)')
        ax2.set_title('圖像處理性能比較')
        ax2.set_xticks(x)
        ax2.set_xticklabels(operations)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('benchmark_results.png', dpi=300, bbox_inches='tight')
        print("📈 性能圖表已保存到: benchmark_results.png")
        
    except ImportError:
        print("⚠️ matplotlib未安裝，跳過圖表生成")
    except Exception as e:
        print(f"⚠️ 生成圖表時出錯: {e}")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='MapleStory Bot GPU性能測試')
    parser.add_argument('--iterations', type=int, default=50, help='測試迭代次數')
    parser.add_argument('--no-plot', action='store_true', help='不生成圖表')
    parser.add_argument('--output', type=str, default='benchmark_results.json', help='結果輸出文件')
    
    args = parser.parse_args()
    
    print("🚀 MapleStory Bot GPU性能測試")
    print("=" * 50)
    
    # 顯示GPU資訊
    gpu_info = get_gpu_info()
    print(f"GPU資訊: {gpu_info}")
    
    if not gpu_info['gpu_available']:
        print("⚠️ GPU不可用，某些測試可能無法正常運行")
    
    # 載入測試圖像
    print("\n📁 載入測試圖像...")
    test_image, templates = load_test_images()
    print(f"測試圖像尺寸: {test_image.shape}")
    print(f"模板數量: {len(templates)}")
    
    # 執行性能測試
    template_results = benchmark_template_matching(test_image, templates, args.iterations)
    processing_results = benchmark_image_processing(test_image, args.iterations)
    
    # 計算總體性能提升
    avg_template_speedup = np.mean([
        template_results['cpu_times'][i] / template_results['gpu_times'][i] 
        for i in range(len(template_results['cpu_times']))
        if template_results['gpu_times'][i] > 0
    ]) if template_results['cpu_times'] else 0
    
    avg_processing_speedup = np.mean([
        processing_results[op]['speedup'] for op in processing_results
    ])
    
    print(f"\n📊 總體性能摘要")
    print("=" * 50)
    print(f"平均模板匹配加速比: {avg_template_speedup:.2f}x")
    print(f"平均圖像處理加速比: {avg_processing_speedup:.2f}x")
    
    # 保存結果
    save_results(template_results, processing_results, args.output)
    
    # 生成圖表
    if not args.no_plot:
        plot_results(template_results, processing_results)
    
    print("\n✅ 性能測試完成！")

if __name__ == "__main__":
    main()
