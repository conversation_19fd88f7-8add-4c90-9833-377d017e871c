# ====== Customized Configuration ======
# Place your personal settings here.
# These will overwrite values from config_default.yaml.
# Focus on tuning here — check config_default.yaml for parameter full descriptions.

# To use import the following configuration. Please use --cfg na when running script

# Note that you can create your own config_XXXXX.yaml and assign it to script by
# adding this arguments: --cfg XXXXX
# -----------------------------------------

# 🎯 KEY BINDINGS
# Match these to your in-game keys. Uncomment once you're done.
key:
  aoe_skill: "r"            # 💥 Set to the AoE skill key. Choose a key with low cooldown.
  directional_attack: "w"   # ⚔️ Assign to your main spammable attack.
  teleport: "e"             # 🌀 Leave empty ("") if your class can’t teleport.
  jump: "space"             # 🦘 Set to jump. No need to change unless you use a non-standard key.
  add_hp: "q"               # ❤️ Assign to HP potion hotkey. Match with your potion shortcut.
  add_mp: "2"               # 💧 Assign to MP potion hotkey. Match with your potion shortcut.

# 🧙 BUFF SKILLS
# Add keys for buffs you want to auto-cast. Match cooldowns with actual in-game durations.
# buff_skill:
#   keys: ["s"]          # ⌨️ Add one or more buff keys here (in order).
#   cooldown: [100]       # ⏱️ Set each skill’s cooldown (seconds). Be precise to avoid spamming.

# ⚔️ DIRECTIONAL ATTACK SETTINGS
# Tune these by observing the debug window.
# Smaller range -> you will fight monster in close-range combat
# Larger range -> you will attack monster far away, but could hit nothing if it's set to large
directional_attack:
  range_x: 250              # ↔️ Try reducing if you’re hitting unreachable monsters.
  range_y: 60               # ↕️

# 💥 AOE SKILL SETTINGS
# Similar to directional attack. Adjust to match your AoE hitbox on screen.
# aoe_skill:
  # Multiple heal
  # range_x: 350               # ↔️ Try reducing if you’re hitting unreachable monsters.
  # range_y: 170               # ↕️
  # Holy light
  # range_x: 400               # ↔️ Try reducing if you’re hitting unreachable monsters.
  # range_y: 170               # ↕️

# ❤️ HEALTH MONITOR
# Use this to auto-drink potions at specific HP/MP thresholds.
# Cleric use skill to heal; therefore, need to set force_heal to False
health_monitor:
  enable: True              # ✅ Set to False if using pet auto-heal.
  force_heal: False          # 🛡️ Heal first — stop attacking until HP is restored
  add_hp_ratio: 0.5         # ❤️ drink potion when HP is below this number
  add_mp_ratio: 0.5         # 💙 drink potion when MP is below this number

game_window:
  title: "MapleStory Worlds-Artale(N.A)"  # 🎮 Game window title (used to detect the correct window)
  size: [752, 1282]                              # 📐 Game window size [height, width] in pixels

# NA server's use english rune warning, so it's longer than TW server
rune_warning:
  top_left: [432, 204]        # 🟦 Top-left corner of the rune warning message
  bottom_right: [864, 230]    # 🟥 Bottom-right corner of the rune warning

# minimap:
#   debug_window_upscale: 4
#   offset: [-3, 31]                  # 📐 Don't set this unless playing on N.A server

system:
  server: "NA" # North America server
  language: "english"

# Sent Email to user to get pass lie detector
email:
  enable: True
