#!/usr/bin/env python3
"""
GPU功能測試腳本
快速驗證GPU加速是否正常工作
"""

import cv2
import numpy as np
import time

def test_gpu_availability():
    """測試GPU可用性"""
    print("🔍 檢查GPU可用性...")
    
    try:
        from gpu_utils import gpu_manager, get_gpu_info
        
        gpu_info = get_gpu_info()
        print(f"GPU資訊: {gpu_info}")
        
        if gpu_info['gpu_available']:
            print("✅ GPU加速可用")
            return True
        else:
            print("❌ GPU加速不可用")
            return False
            
    except ImportError as e:
        print(f"❌ 無法導入GPU模組: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU檢查失敗: {e}")
        return False

def test_opencv_cuda():
    """測試OpenCV CUDA支援"""
    print("\n🔍 檢查OpenCV CUDA支援...")
    
    try:
        cuda_devices = cv2.cuda.getCudaEnabledDeviceCount()
        print(f"CUDA設備數量: {cuda_devices}")
        
        if cuda_devices > 0:
            print("✅ OpenCV CUDA支援正常")
            return True
        else:
            print("❌ 未檢測到CUDA設備")
            return False
            
    except AttributeError:
        print("❌ OpenCV未編譯CUDA支援")
        return False
    except Exception as e:
        print(f"❌ CUDA檢查失敗: {e}")
        return False

def test_gpu_functions():
    """測試GPU函數"""
    print("\n🔍 測試GPU函數...")
    
    try:
        from gpu_utils import gpu_template_matching, gpu_gaussian_blur, gpu_resize, gpu_cvt_color
        
        # 創建測試圖像
        test_image = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
        test_template = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        
        print("測試模板匹配...")
        result = gpu_template_matching(test_image, test_template, cv2.TM_SQDIFF_NORMED)
        print(f"  模板匹配結果形狀: {result.shape}")
        
        print("測試高斯模糊...")
        blurred = gpu_gaussian_blur(test_image, (15, 15), 5)
        print(f"  模糊結果形狀: {blurred.shape}")
        
        print("測試圖像縮放...")
        resized = gpu_resize(test_image, (300, 200))
        print(f"  縮放結果形狀: {resized.shape}")
        
        print("測試顏色轉換...")
        gray = gpu_cvt_color(test_image, cv2.COLOR_BGR2GRAY)
        print(f"  灰度圖形狀: {gray.shape}")
        
        print("✅ 所有GPU函數測試通過")
        return True
        
    except Exception as e:
        print(f"❌ GPU函數測試失敗: {e}")
        return False

def test_util_functions():
    """測試util.py中的GPU包裝函數"""
    print("\n🔍 測試util.py GPU包裝函數...")
    
    try:
        from util import gpu_gaussian_blur_wrapper, gpu_resize_wrapper, gpu_cvt_color_wrapper, find_pattern_sqdiff
        
        # 創建測試圖像
        test_image = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
        test_template = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        
        print("測試GPU包裝函數...")
        
        # 測試高斯模糊包裝
        blurred = gpu_gaussian_blur_wrapper(test_image, (5, 5), 1, use_gpu=True)
        print(f"  高斯模糊包裝: {blurred.shape}")
        
        # 測試縮放包裝
        resized = gpu_resize_wrapper(test_image, (300, 200), use_gpu=True)
        print(f"  縮放包裝: {resized.shape}")
        
        # 測試顏色轉換包裝
        gray = gpu_cvt_color_wrapper(test_image, cv2.COLOR_BGR2GRAY, use_gpu=True)
        print(f"  顏色轉換包裝: {gray.shape}")
        
        # 測試find_pattern_sqdiff的GPU參數
        loc, score, cached = find_pattern_sqdiff(test_image, test_template, use_gpu=True)
        print(f"  模板匹配: 位置={loc}, 分數={score:.4f}, 快取={cached}")
        
        print("✅ util.py GPU包裝函數測試通過")
        return True
        
    except Exception as e:
        print(f"❌ util.py GPU函數測試失敗: {e}")
        return False

def performance_comparison():
    """簡單的性能比較"""
    print("\n🔍 簡單性能比較...")
    
    try:
        from gpu_utils import gpu_template_matching
        
        # 創建較大的測試圖像
        test_image = np.random.randint(0, 255, (800, 1200, 3), dtype=np.uint8)
        test_template = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        iterations = 10
        
        # CPU測試
        print(f"CPU模板匹配測試 ({iterations}次)...")
        start_time = time.time()
        for _ in range(iterations):
            cv2.matchTemplate(test_image, test_template, cv2.TM_SQDIFF_NORMED)
        cpu_time = time.time() - start_time
        
        # GPU測試
        print(f"GPU模板匹配測試 ({iterations}次)...")
        start_time = time.time()
        for _ in range(iterations):
            gpu_template_matching(test_image, test_template, cv2.TM_SQDIFF_NORMED)
        gpu_time = time.time() - start_time
        
        speedup = cpu_time / gpu_time if gpu_time > 0 else 0
        
        print(f"CPU時間: {cpu_time:.4f}秒")
        print(f"GPU時間: {gpu_time:.4f}秒")
        print(f"加速比: {speedup:.2f}x")
        
        if speedup > 1:
            print("✅ GPU加速有效")
        else:
            print("⚠️ GPU加速效果不明顯")
            
        return True
        
    except Exception as e:
        print(f"❌ 性能比較失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 GPU功能測試開始")
    print("=" * 50)
    
    tests = [
        ("GPU可用性", test_gpu_availability),
        ("OpenCV CUDA", test_opencv_cuda),
        ("GPU函數", test_gpu_functions),
        ("util包裝函數", test_util_functions),
        ("性能比較", performance_comparison)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
    
    print(f"\n{'='*50}")
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！GPU加速已準備就緒。")
    elif passed > 0:
        print("⚠️ 部分測試通過，GPU加速可能部分可用。")
    else:
        print("❌ 所有測試失敗，GPU加速不可用。")
    
    print("\n💡 提示:")
    print("- 如果GPU測試失敗，機器人會自動使用CPU模式")
    print("- 執行 'python benchmark.py' 進行完整性能測試")
    print("- 查看 GPU_ACCELERATION_GUIDE.md 獲取詳細說明")

if __name__ == "__main__":
    main()
