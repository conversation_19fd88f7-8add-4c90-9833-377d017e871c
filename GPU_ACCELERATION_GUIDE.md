# 🚀 MapleStory自動練級機器人 GPU加速指南

本指南將幫助您為MapleStory自動練級機器人啟用GPU加速，大幅提升圖像處理性能。

## 📋 系統需求

### 硬體需求
- **NVIDIA GPU**: 支援CUDA的NVIDIA顯示卡 (GTX 1050或更高)
- **顯存**: 建議4GB或以上
- **RAM**: 建議8GB或以上

### 軟體需求
- **CUDA Toolkit**: 11.x 或 12.x 版本
- **Python**: 3.8 或更高版本
- **Windows**: Windows 10/11 (主要支援平台)

## 🔧 安裝步驟

### 1. 安裝CUDA Toolkit

1. 前往 [NVIDIA CUDA下載頁面](https://developer.nvidia.com/cuda-downloads)
2. 選擇您的作業系統和版本
3. 下載並安裝CUDA Toolkit
4. 重啟電腦

### 2. 驗證CUDA安裝

打開命令提示字元，執行：
```bash
nvcc --version
nvidia-smi
```

如果看到版本資訊，表示CUDA安裝成功。

### 3. 安裝GPU加速依賴

在專案目錄中執行：

```bash
# 安裝GPU版本的OpenCV和相關依賴
pip install -r requirements.txt

# 如果遇到問題，可以手動安裝
pip install opencv-contrib-python>=4.8.0
pip install cupy-cuda12x  # 對於CUDA 12.x
# 或
pip install cupy-cuda11x  # 對於CUDA 11.x
```

### 4. 配置GPU設定

編輯 `config/config_default.yaml` 文件：

```yaml
gpu:
  enable: True                      # 啟用GPU加速
  device_id: 0                      # GPU設備ID (0為第一張顯示卡)
  fallback_to_cpu: True             # GPU失敗時自動回退到CPU
  
  # GPU加速的具體操作
  template_matching: True           # 模板匹配 (最大性能提升)
  image_processing: True            # 圖像處理 (模糊、縮放、顏色轉換)
  
  # 記憶體管理
  memory_pool_size: 512             # GPU記憶體池大小 (MB)
  clear_memory_interval: 100        # 每N幀清理一次記憶體
```

## 🎯 使用方法

### 啟動機器人

正常啟動機器人，GPU加速會自動啟用：

```bash
python mapleStoryAutoLevelUp.py --cfg your_config
```

### 檢查GPU狀態

機器人啟動時會顯示GPU資訊：
```
[INFO] GPU加速已啟用: {'gpu_available': True, 'cuda_available': True, 'device_count': 1, 'current_device': 0}
```

如果看到警告訊息，表示GPU不可用，將自動使用CPU模式。

### 性能測試

執行性能基準測試：

```bash
# 基本測試
python benchmark.py

# 自定義測試參數
python benchmark.py --iterations 100 --output my_benchmark.json

# 不生成圖表
python benchmark.py --no-plot
```

## 📊 性能提升預期

根據測試，GPU加速可以帶來以下性能提升：

| 操作類型 | 預期加速比 | 說明 |
|---------|-----------|------|
| 模板匹配 | 2-5x | 最大的性能提升來源 |
| 高斯模糊 | 1.5-3x | 圖像預處理加速 |
| 圖像縮放 | 1.2-2x | 幀處理加速 |
| 顏色轉換 | 1.3-2.5x | 色彩空間轉換加速 |

**總體性能提升**: 通常可以獲得 **2-4倍** 的整體性能提升。

## 🔧 故障排除

### 常見問題

#### 1. "GPU工具模組未找到"
```
解決方案：
- 確保 gpu_utils.py 文件存在
- 檢查 Python 路徑設定
```

#### 2. "未檢測到CUDA支援的GPU"
```
解決方案：
- 確認您的GPU支援CUDA
- 檢查CUDA驅動程式是否正確安裝
- 執行 nvidia-smi 確認GPU狀態
```

#### 3. "opencv-contrib-python 安裝失敗"
```
解決方案：
- 先卸載舊版本：pip uninstall opencv-python opencv-contrib-python
- 重新安裝：pip install opencv-contrib-python>=4.8.0
```

#### 4. "cupy 安裝失敗"
```
解決方案：
- 確認CUDA版本：nvcc --version
- 安裝對應版本的cupy：
  - CUDA 11.x: pip install cupy-cuda11x
  - CUDA 12.x: pip install cupy-cuda12x
```

### 記憶體問題

如果遇到GPU記憶體不足：

1. 降低 `memory_pool_size` 設定
2. 減少 `clear_memory_interval` 值
3. 關閉部分GPU加速功能：
   ```yaml
   gpu:
     template_matching: True    # 保留最重要的加速
     image_processing: False   # 關閉圖像處理加速
   ```

### 性能調優

1. **監控GPU使用率**：
   ```bash
   nvidia-smi -l 1  # 每秒更新一次
   ```

2. **調整記憶體設定**：
   - 增加 `memory_pool_size` 可能提升性能
   - 但要確保不超過GPU記憶體限制

3. **選擇性啟用加速**：
   - 如果某些操作在GPU上較慢，可以單獨關閉
   - 保留模板匹配加速通常能獲得最大收益

## 🔄 回退到CPU模式

如果需要暫時關閉GPU加速：

1. **配置文件方式**：
   ```yaml
   gpu:
     enable: False
   ```

2. **臨時關閉**：
   - 重命名或刪除 `gpu_utils.py` 文件
   - 機器人會自動回退到CPU模式

## 📈 監控和優化

### 性能監控

1. **查看日誌**：機器人會記錄GPU使用狀況
2. **運行基準測試**：定期執行 `benchmark.py` 檢查性能
3. **監控系統資源**：使用工作管理員或 `nvidia-smi` 監控

### 優化建議

1. **保持驅動程式更新**：定期更新NVIDIA驅動程式
2. **關閉不必要的程式**：釋放GPU記憶體
3. **調整遊戲設定**：降低遊戲畫質可以減少GPU負載衝突

## 🆘 獲取幫助

如果遇到問題：

1. 檢查日誌文件中的錯誤訊息
2. 執行 `python benchmark.py` 進行診斷
3. 確認系統符合所有需求
4. 嘗試重新安裝相關依賴

---

**注意**: GPU加速功能需要適當的硬體支援。如果您的系統不支援CUDA，機器人會自動使用CPU模式，功能不會受到影響。
