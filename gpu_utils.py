"""
GPU加速工具模組
提供GPU初始化、記憶體管理和GPU加速的圖像處理函數
"""

import cv2
import numpy as np
import logging
from typing import Optional, Tuple, Union
import time

# 設置日誌
logger = logging.getLogger(__name__)

class GPUManager:
    """GPU管理器，負責GPU初始化和狀態管理"""
    
    def __init__(self):
        self.gpu_available = False
        self.cuda_available = False
        self.gpu_device_count = 0
        self.current_device = 0
        self.gpu_memory_info = {}
        
        self._initialize_gpu()
    
    def _initialize_gpu(self):
        """初始化GPU環境"""
        try:
            # 檢查OpenCV是否支援CUDA
            self.cuda_available = cv2.cuda.getCudaEnabledDeviceCount() > 0
            
            if self.cuda_available:
                self.gpu_device_count = cv2.cuda.getCudaEnabledDeviceCount()
                logger.info(f"檢測到 {self.gpu_device_count} 個CUDA設備")
                
                # 設置GPU設備
                cv2.cuda.setDevice(self.current_device)
                
                # 獲取GPU記憶體資訊
                self._update_gpu_memory_info()
                
                self.gpu_available = True
                logger.info(f"GPU加速已啟用，使用設備 {self.current_device}")
                logger.info(f"GPU記憶體: {self.gpu_memory_info}")
                
            else:
                logger.warning("未檢測到CUDA支援的GPU，將使用CPU模式")
                
        except Exception as e:
            logger.error(f"GPU初始化失敗: {e}")
            self.gpu_available = False
    
    def _update_gpu_memory_info(self):
        """更新GPU記憶體資訊"""
        try:
            if self.cuda_available:
                # 注意：OpenCV的CUDA模組可能不提供記憶體查詢功能
                # 這裡提供一個基本的實現
                self.gpu_memory_info = {
                    'device_id': self.current_device,
                    'device_count': self.gpu_device_count
                }
        except Exception as e:
            logger.warning(f"無法獲取GPU記憶體資訊: {e}")
    
    def is_gpu_available(self) -> bool:
        """檢查GPU是否可用"""
        return self.gpu_available
    
    def get_device_info(self) -> dict:
        """獲取GPU設備資訊"""
        return {
            'gpu_available': self.gpu_available,
            'cuda_available': self.cuda_available,
            'device_count': self.gpu_device_count,
            'current_device': self.current_device,
            'memory_info': self.gpu_memory_info
        }

# 全局GPU管理器實例
gpu_manager = GPUManager()

def get_gpu_info() -> dict:
    """
    獲取GPU資訊的便利函數

    Returns:
        GPU設備資訊字典
    """
    return gpu_manager.get_device_info()

class GPUImage:
    """GPU圖像包裝器，用於管理GPU記憶體中的圖像"""
    
    def __init__(self, image: Union[np.ndarray, cv2.cuda.GpuMat]):
        if isinstance(image, np.ndarray):
            # CPU圖像上傳到GPU
            self.gpu_mat = cv2.cuda.GpuMat()
            self.gpu_mat.upload(image)
        elif isinstance(image, cv2.cuda.GpuMat):
            self.gpu_mat = image
        else:
            raise ValueError("不支援的圖像類型")
    
    def download(self) -> np.ndarray:
        """從GPU下載圖像到CPU"""
        cpu_image = np.empty(self.gpu_mat.size(), dtype=np.uint8)
        self.gpu_mat.download(cpu_image)
        return cpu_image
    
    def size(self) -> Tuple[int, int]:
        """獲取圖像尺寸"""
        return self.gpu_mat.size()
    
    def __del__(self):
        """清理GPU記憶體"""
        if hasattr(self, 'gpu_mat'):
            del self.gpu_mat

def gpu_template_matching(image: np.ndarray, 
                         template: np.ndarray,
                         method: int = cv2.TM_SQDIFF_NORMED,
                         mask: Optional[np.ndarray] = None) -> np.ndarray:
    """
    GPU加速的模板匹配
    
    Args:
        image: 搜索圖像
        template: 模板圖像
        method: 匹配方法
        mask: 遮罩（可選）
    
    Returns:
        匹配結果矩陣
    """
    if not gpu_manager.is_gpu_available():
        # 回退到CPU版本
        return cv2.matchTemplate(image, template, method, mask=mask)
    
    try:
        # 上傳圖像到GPU
        gpu_image = cv2.cuda.GpuMat()
        gpu_template = cv2.cuda.GpuMat()
        gpu_image.upload(image)
        gpu_template.upload(template)
        
        # GPU模板匹配
        gpu_result = cv2.cuda.GpuMat()
        
        if mask is not None:
            gpu_mask = cv2.cuda.GpuMat()
            gpu_mask.upload(mask)
            cv2.cuda.matchTemplate(gpu_image, gpu_template, gpu_result, method, gpu_mask)
        else:
            cv2.cuda.matchTemplate(gpu_image, gpu_template, gpu_result, method)
        
        # 下載結果
        result = gpu_result.download()
        
        return result
        
    except Exception as e:
        logger.warning(f"GPU模板匹配失敗，回退到CPU: {e}")
        return cv2.matchTemplate(image, template, method, mask=mask)

def gpu_gaussian_blur(image: np.ndarray, 
                     ksize: Tuple[int, int], 
                     sigmaX: float, 
                     sigmaY: float = 0) -> np.ndarray:
    """
    GPU加速的高斯模糊
    
    Args:
        image: 輸入圖像
        ksize: 核大小
        sigmaX: X方向標準差
        sigmaY: Y方向標準差
    
    Returns:
        模糊後的圖像
    """
    if not gpu_manager.is_gpu_available():
        return cv2.GaussianBlur(image, ksize, sigmaX, sigmaY)
    
    try:
        gpu_image = cv2.cuda.GpuMat()
        gpu_image.upload(image)
        
        gpu_result = cv2.cuda.GpuMat()
        cv2.cuda.bilateralFilter(gpu_image, gpu_result, -1, sigmaX, sigmaY)
        
        return gpu_result.download()
        
    except Exception as e:
        logger.warning(f"GPU高斯模糊失敗，回退到CPU: {e}")
        return cv2.GaussianBlur(image, ksize, sigmaX, sigmaY)

def gpu_resize(image: np.ndarray, 
               size: Tuple[int, int], 
               interpolation: int = cv2.INTER_LINEAR) -> np.ndarray:
    """
    GPU加速的圖像縮放
    
    Args:
        image: 輸入圖像
        size: 目標尺寸 (width, height)
        interpolation: 插值方法
    
    Returns:
        縮放後的圖像
    """
    if not gpu_manager.is_gpu_available():
        return cv2.resize(image, size, interpolation=interpolation)
    
    try:
        gpu_image = cv2.cuda.GpuMat()
        gpu_image.upload(image)
        
        gpu_result = cv2.cuda.GpuMat()
        cv2.cuda.resize(gpu_image, gpu_result, size, interpolation=interpolation)
        
        return gpu_result.download()
        
    except Exception as e:
        logger.warning(f"GPU圖像縮放失敗，回退到CPU: {e}")
        return cv2.resize(image, size, interpolation=interpolation)

def gpu_cvt_color(image: np.ndarray, code: int) -> np.ndarray:
    """
    GPU加速的顏色空間轉換
    
    Args:
        image: 輸入圖像
        code: 轉換代碼
    
    Returns:
        轉換後的圖像
    """
    if not gpu_manager.is_gpu_available():
        return cv2.cvtColor(image, code)
    
    try:
        gpu_image = cv2.cuda.GpuMat()
        gpu_image.upload(image)
        
        gpu_result = cv2.cuda.GpuMat()
        cv2.cuda.cvtColor(gpu_image, gpu_result, code)
        
        return gpu_result.download()
        
    except Exception as e:
        logger.warning(f"GPU顏色轉換失敗，回退到CPU: {e}")
        return cv2.cvtColor(image, code)

def benchmark_gpu_vs_cpu(image: np.ndarray, template: np.ndarray, iterations: int = 100):
    """
    GPU vs CPU性能基準測試
    
    Args:
        image: 測試圖像
        template: 模板圖像
        iterations: 測試迭代次數
    
    Returns:
        性能比較結果
    """
    print("開始GPU vs CPU性能測試...")
    
    # CPU測試
    start_time = time.time()
    for _ in range(iterations):
        cv2.matchTemplate(image, template, cv2.TM_SQDIFF_NORMED)
    cpu_time = time.time() - start_time
    
    # GPU測試
    start_time = time.time()
    for _ in range(iterations):
        gpu_template_matching(image, template, cv2.TM_SQDIFF_NORMED)
    gpu_time = time.time() - start_time
    
    speedup = cpu_time / gpu_time if gpu_time > 0 else 0
    
    results = {
        'cpu_time': cpu_time,
        'gpu_time': gpu_time,
        'speedup': speedup,
        'iterations': iterations,
        'gpu_available': gpu_manager.is_gpu_available()
    }
    
    print(f"CPU時間: {cpu_time:.4f}秒")
    print(f"GPU時間: {gpu_time:.4f}秒")
    print(f"加速比: {speedup:.2f}x")
    
    return results
